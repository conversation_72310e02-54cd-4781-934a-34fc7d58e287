# Robot World - Requirements Analysis

## Project Overview
Robot World is a multi-iteration team project requiring a client-server architecture where robots operate in a grid-based world with obstacles, shields, and weapons.

## Server Requirements Analysis

### World Specification
- **Grid-based world**: Configurable size (width x height in kliks)
- **Coordinate system**: Top-left [0,0], bottom-right [width-1, height-1]
- **Directions**: Standard compass (N, S, E, W)
- **Obstacles**: Mountains, lakes, bottomless pits (rectangular shapes)
  - Mountains: Block movement and vision
  - Lakes: Block movement, allow vision
  - Bottomless pits: Destroy robots that enter
- **Visibility**: Configurable range in steps

### World Commands
- `quit`: Disconnect all robots and end world
- `robots`: List all robots with name and state
- `dump`: Display world state representation

### Configuration Parameters
- World size (width, height)
- Visibility range
- Shield repair time
- Weapon reload time
- Maximum shield strength

## Client Requirements Analysis

### Robot Capabilities
- **Movement**: forward, back, turn left/right
- **Orientation**: Query current facing direction
- **Vision**: Look around within visibility range
- **Combat**: Fire weapons, shields for defense
- **Maintenance**: Repair shields, reload weapons
- **Robot Makes**: Different configurations (e.g., <PERSON><PERSON><PERSON>, <PERSON>)

### Commands
- `launch <make> <name>`: Enter world
- `forward <steps>`, `back <steps>`: Movement
- `turn <left/right>`: Change direction
- `orientation`: Get current direction
- `look`: Scan surroundings
- `fire`: Shoot weapon
- `repair`: Fix shields
- `reload`: Reload weapon
- `state`: Get robot status

## Protocol Requirements
- Client-server communication over TCP/IP
- JSON-based message format
- Request-response pattern
- Support for multiple concurrent clients

## Technical Constraints
- Java implementation
- No hard-coded values (use configuration files)
- Comprehensive unit testing required
- GitLab collaboration with issues and wiki
- Pull request workflow mandatory
