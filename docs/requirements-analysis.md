# Robot World - Requirements Analysis

## Project Overview
Robot World is a multi-iteration team project requiring a client-server architecture where robots operate in a grid-based world with obstacles, shields, and weapons.

## Server Requirements Analysis

### World Specification
- **Grid-based world**: Configurable size (width x height in kliks)
- **Coordinate system**: Top-left [0,0], bottom-right [width-1, height-1]
- **Directions**: Standard compass (N, S, E, W)
- **Obstacles**: Mountains, lakes, bottomless pits (rectangular shapes)
  - Mountains: Block movement and vision
  - Lakes: Block movement, allow vision
  - Bottomless pits: Destroy robots that enter
- **Visibility**: Configurable range in steps

### World Commands
- `quit`: Disconnect all robots and end world
- `robots`: List all robots with name and state
- `dump`: Display world state representation

### Configuration Parameters
- World size (width, height)
- Visibility range
- Shield repair time
- Weapon reload time
- Maximum shield strength

## Client Requirements Analysis

### Robot Capabilities
- **Movement**: forward, back, turn left/right
- **Orientation**: Query current facing direction
- **Vision**: Look around within visibility range
- **Combat**: Fire weapons, shields for defense
- **Maintenance**: Repair shields, reload weapons
- **Robot Makes**: Different configurations (e.g., <PERSON><PERSON><PERSON>, <PERSON>)

### Commands
- `launch <make> <name>`: Enter world
- `forward <steps>`, `back <steps>`: Movement
- `turn <left/right>`: Change direction
- `orientation`: Get current direction
- `look`: Scan surroundings
- `fire`: Shoot weapon
- `repair`: Fix shields
- `reload`: Reload weapon
- `state`: Get robot status

## Protocol Requirements
- Client-server communication over TCP/IP
- JSON-based message format
- Request-response pattern
- Support for multiple concurrent clients

## Technical Constraints
- Java implementation
- No hard-coded values (use configuration files)
- Comprehensive unit testing required
- GitLab collaboration with issues and wiki
- Pull request workflow mandatory

## Design Documentation

### Class Diagram
The class diagram shows the most important classes and their relationships in the Robot World system:

```mermaid
classDiagram
    class World {
        -int width
        -int height
        -int visibilityRange
        -List~Robot~ robots
        -List~Obstacle~ obstacles
        -WorldConfiguration config
        +addRobot(Robot robot) boolean
        +removeRobot(String name) boolean
        +getRobots() List~Robot~
        +getObstacles() List~Obstacle~
        +isValidPosition(Position pos) boolean
        +isObstructed(Position pos) boolean
        +getRobotsInRange(Position pos, int range) List~Robot~
        +dump() String
    }

    class Robot {
        -String name
        -String make
        -Position position
        -Direction direction
        -Shield shield
        -Weapon weapon
        -RobotState state
        +move(int steps) boolean
        +turn(Direction dir) void
        +look() LookResult
        +fire() boolean
        +repair() void
        +reload() void
        +getState() RobotState
        +takeDamage() boolean
        +isAlive() boolean
    }

    class Position {
        -int x
        -int y
        +getX() int
        +getY() int
        +distanceTo(Position other) int
        +equals(Object obj) boolean
    }

    class Direction {
        <<enumeration>>
        NORTH
        SOUTH
        EAST
        WEST
        +turnLeft() Direction
        +turnRight() Direction
        +opposite() Direction
    }

    class Shield {
        -int currentStrength
        -int maxStrength
        -boolean repairing
        +takeDamage() boolean
        +repair(int repairTime) void
        +isDestroyed() boolean
        +getStrength() int
    }

    class Weapon {
        -int shots
        -int maxShots
        -int range
        -boolean reloading
        +fire() boolean
        +reload(int reloadTime) void
        +canFire() boolean
        +getShots() int
    }

    class Obstacle {
        <<abstract>>
        -Position topLeft
        -Position bottomRight
        +contains(Position pos) boolean
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class Mountain {
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class Lake {
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class BottomlessPit {
        +blocksMovement() boolean
        +blocksVision() boolean
        +destroysRobot() boolean
    }

    class WorldConfiguration {
        -int worldWidth
        -int worldHeight
        -int visibilityRange
        -int shieldRepairTime
        -int weaponReloadTime
        -int maxShieldStrength
        +loadFromFile(String filename) WorldConfiguration
    }

    class RobotState {
        -Position position
        -Direction direction
        -int shieldStrength
        -int shots
        -String status
    }

    class LookResult {
        -List~Obstacle~ obstacles
        -List~Robot~ robots
        -List~String~ edges
    }

    %% Relationships
    World "1" --> "*" Robot : contains
    World "1" --> "*" Obstacle : contains
    World "1" --> "1" WorldConfiguration : uses
    Robot "1" --> "1" Position : has
    Robot "1" --> "1" Direction : faces
    Robot "1" --> "1" Shield : has
    Robot "1" --> "1" Weapon : has
    Robot "1" --> "1" RobotState : provides
    Obstacle <|-- Mountain : extends
    Obstacle <|-- Lake : extends
    Obstacle <|-- BottomlessPit : extends
    Obstacle "1" --> "2" Position : defined by
    Robot --> LookResult : creates
```

### Domain Diagram
The domain diagram characterizes the domain classes and how they work together:

```mermaid
graph TB
    subgraph "Game World Domain"
        World[World<br/>Grid-based battlefield<br/>Configurable size]
        Grid[Grid System<br/>Coordinate system<br/>Position tracking]
        Visibility[Visibility System<br/>Line of sight<br/>Range limitations]
    end

    subgraph "Robot Domain"
        Robot[Robot<br/>Player entity<br/>Named & typed]
        Movement[Movement System<br/>Forward/back/turn<br/>Obstacle avoidance]
        Orientation[Orientation<br/>N/S/E/W facing<br/>Direction tracking]
    end

    subgraph "Combat Domain"
        Weapon[Weapon System<br/>Shots & range<br/>Reload mechanics]
        Shield[Shield System<br/>Damage absorption<br/>Repair mechanics]
        Combat[Combat Resolution<br/>Hit detection<br/>Damage calculation]
    end

    subgraph "Obstacle Domain"
        Mountain[Mountain<br/>Blocks movement<br/>Blocks vision]
        Lake[Lake<br/>Blocks movement<br/>Allows vision]
        Pit[Bottomless Pit<br/>Destroys robots<br/>Allows vision]
    end

    subgraph "Configuration Domain"
        WorldConfig[World Configuration<br/>Size, visibility<br/>Timing parameters]
        RobotMakes[Robot Makes<br/>Different configurations<br/>Sniper, Tank, etc.]
    end

    subgraph "Communication Domain"
        Protocol[TCP/IP Protocol<br/>JSON messages<br/>Request-response]
        Commands[Command System<br/>Robot actions<br/>World queries]
    end

    %% Domain Relationships
    World --> Grid
    World --> Visibility
    World --> Mountain
    World --> Lake
    World --> Pit
    World --> Robot

    Robot --> Movement
    Robot --> Orientation
    Robot --> Weapon
    Robot --> Shield
    Robot --> Combat

    Movement --> Grid
    Movement --> Mountain
    Movement --> Lake
    Movement --> Pit

    Visibility --> Grid
    Visibility --> Mountain

    Combat --> Weapon
    Combat --> Shield
    Combat --> Robot

    WorldConfig --> World
    WorldConfig --> Visibility
    WorldConfig --> Shield
    WorldConfig --> Weapon

    RobotMakes --> Robot
    RobotMakes --> Weapon
    RobotMakes --> Shield

    Protocol --> Commands
    Commands --> Robot
    Commands --> World

    %% Key interactions
    Robot -.->|"looks around"| Visibility
    Robot -.->|"fires at"| Robot
    Robot -.->|"moves through"| Grid
    Weapon -.->|"limited by"| Visibility
    Shield -.->|"protects from"| Combat
```

### Client Component Diagram
The component diagram for the client shows the architecture and components of the robot client application:

```mermaid
graph TB
    subgraph "Client Application"
        subgraph "User Interface Layer"
            CLI[Command Line Interface<br/>User input/output<br/>Command parsing]
            Display[Display Manager<br/>World visualization<br/>Status reporting]
        end

        subgraph "Application Layer"
            ClientApp[Client Application<br/>Main controller<br/>Command coordination]
            RobotController[Robot Controller<br/>Robot state management<br/>Action execution]
            CommandProcessor[Command Processor<br/>Input validation<br/>Command routing]
        end

        subgraph "Domain Layer"
            RobotModel[Robot Model<br/>Local robot state<br/>Position & status]
            WorldView[World View<br/>Local world knowledge<br/>Obstacle & robot tracking]
            RobotMake[Robot Make<br/>Configuration<br/>Capabilities]
        end

        subgraph "Communication Layer"
            NetworkClient[Network Client<br/>TCP connection<br/>Message handling]
            MessageSerializer[Message Serializer<br/>JSON encoding/decoding<br/>Protocol compliance]
            ConnectionManager[Connection Manager<br/>Connection lifecycle<br/>Error handling]
        end

        subgraph "Configuration Layer"
            ConfigLoader[Configuration Loader<br/>Robot make definitions<br/>Client settings]
            RobotFactory[Robot Factory<br/>Robot instantiation<br/>Make-specific setup]
        end
    end

    subgraph "External Systems"
        Server[Robot World Server<br/>TCP/IP endpoint]
        ConfigFiles[Configuration Files<br/>Robot makes<br/>Client settings]
        User[User<br/>Command input<br/>Visual feedback]
    end

    %% Component Relationships
    User --> CLI
    CLI --> CommandProcessor
    CommandProcessor --> ClientApp
    ClientApp --> RobotController
    ClientApp --> Display

    RobotController --> RobotModel
    RobotController --> WorldView
    RobotController --> NetworkClient

    NetworkClient --> MessageSerializer
    NetworkClient --> ConnectionManager
    ConnectionManager --> Server

    ConfigLoader --> ConfigFiles
    ConfigLoader --> RobotFactory
    RobotFactory --> RobotMake
    RobotFactory --> RobotModel

    Display --> WorldView
    Display --> RobotModel

    %% Data Flow
    MessageSerializer -.->|"JSON messages"| Server
    Server -.->|"JSON responses"| MessageSerializer
    WorldView -.->|"world state"| Display
    RobotModel -.->|"robot status"| Display
```

### Server Component Diagram
The component diagram for the server portion that reads and executes commands:

```mermaid
graph TB
    subgraph "Server Application"
        subgraph "Network Layer"
            ServerSocket[Server Socket<br/>TCP listener<br/>Client connections]
            ClientHandler[Client Handler<br/>Per-client threads<br/>Connection management]
            MessageRouter[Message Router<br/>Request routing<br/>Response handling]
        end

        subgraph "Command Processing Layer"
            CommandParser[Command Parser<br/>JSON parsing<br/>Command validation]
            CommandDispatcher[Command Dispatcher<br/>Command routing<br/>Handler selection]
            WorldCommandHandler[World Command Handler<br/>quit, robots, dump<br/>World management]
            RobotCommandHandler[Robot Command Handler<br/>Robot actions<br/>Movement, combat]
        end

        subgraph "Game Engine Layer"
            WorldEngine[World Engine<br/>Game state management<br/>Rule enforcement]
            MovementEngine[Movement Engine<br/>Position validation<br/>Collision detection]
            CombatEngine[Combat Engine<br/>Weapon firing<br/>Damage resolution]
            VisibilityEngine[Visibility Engine<br/>Line of sight<br/>Range calculations]
        end

        subgraph "Domain Layer"
            WorldModel[World Model<br/>Grid state<br/>Robot & obstacle tracking]
            RobotManager[Robot Manager<br/>Robot lifecycle<br/>State management]
            ObstacleManager[Obstacle Manager<br/>Obstacle definitions<br/>Collision queries]
        end

        subgraph "Configuration Layer"
            ConfigManager[Configuration Manager<br/>World settings<br/>Parameter loading]
            WorldFactory[World Factory<br/>World initialization<br/>Obstacle placement]
        end

        subgraph "Persistence Layer"
            StateManager[State Manager<br/>Game state persistence<br/>Session management]
            LogManager[Log Manager<br/>Action logging<br/>Audit trail]
        end
    end

    subgraph "External Systems"
        ConfigFiles[Configuration Files<br/>World parameters<br/>Obstacle definitions]
        Clients[Robot Clients<br/>TCP connections<br/>JSON messages]
        Console[Server Console<br/>Admin commands<br/>World management]
    end

    %% Component Relationships
    Clients --> ServerSocket
    ServerSocket --> ClientHandler
    ClientHandler --> MessageRouter
    MessageRouter --> CommandParser

    CommandParser --> CommandDispatcher
    CommandDispatcher --> WorldCommandHandler
    CommandDispatcher --> RobotCommandHandler

    WorldCommandHandler --> WorldEngine
    RobotCommandHandler --> WorldEngine
    RobotCommandHandler --> MovementEngine
    RobotCommandHandler --> CombatEngine
    RobotCommandHandler --> VisibilityEngine

    WorldEngine --> WorldModel
    WorldEngine --> RobotManager
    WorldEngine --> ObstacleManager

    MovementEngine --> WorldModel
    MovementEngine --> ObstacleManager
    CombatEngine --> RobotManager
    VisibilityEngine --> WorldModel
    VisibilityEngine --> ObstacleManager

    ConfigManager --> ConfigFiles
    ConfigManager --> WorldFactory
    WorldFactory --> WorldModel
    WorldFactory --> ObstacleManager

    StateManager --> WorldModel
    StateManager --> RobotManager
    LogManager --> WorldEngine

    Console --> WorldCommandHandler

    %% Data Flow
    MessageRouter -.->|"JSON responses"| ClientHandler
    WorldModel -.->|"world state"| WorldCommandHandler
    RobotManager -.->|"robot states"| RobotCommandHandler
```

## Design Decisions Summary

### Key Architectural Decisions:
1. **Client-Server Architecture**: Clear separation between robot clients and world server
2. **Component-Based Design**: Modular components with single responsibilities
3. **Domain-Driven Design**: Core domain concepts clearly separated from infrastructure
4. **Configuration-Driven**: No hard-coded values, everything configurable
5. **Concurrent Support**: Server handles multiple clients simultaneously
6. **Protocol Abstraction**: JSON-based communication protocol

### Design Patterns Used:
- **Factory Pattern**: For creating robots and world instances
- **Command Pattern**: For processing robot and world commands
- **Observer Pattern**: For state change notifications
- **Strategy Pattern**: For different robot makes and obstacle behaviors
