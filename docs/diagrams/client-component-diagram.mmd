graph TB
    subgraph "Client Application"
        subgraph "User Interface Layer"
            CLI[Command Line Interface<br/>User input/output<br/>Command parsing]
            Display[Display Manager<br/>World visualization<br/>Status reporting]
        end

        subgraph "Application Layer"
            ClientApp[Client Application<br/>Main controller<br/>Command coordination]
            RobotController[Robot Controller<br/>Robot state management<br/>Action execution]
            CommandProcessor[Command Processor<br/>Input validation<br/>Command routing]
        end

        subgraph "Domain Layer"
            RobotModel[Robot Model<br/>Local robot state<br/>Position & status]
            WorldView[World View<br/>Local world knowledge<br/>Obstacle & robot tracking]
            RobotMake[Robot Make<br/>Configuration<br/>Capabilities]
        end

        subgraph "Communication Layer"
            NetworkClient[Network Client<br/>TCP connection<br/>Message handling]
            MessageSerializer[Message Serializer<br/>JSON encoding/decoding<br/>Protocol compliance]
            ConnectionManager[Connection Manager<br/>Connection lifecycle<br/>Error handling]
        end

        subgraph "Configuration Layer"
            ConfigLoader[Configuration Loader<br/>Robot make definitions<br/>Client settings]
            RobotFactory[Robot Factory<br/>Robot instantiation<br/>Make-specific setup]
        end
    end

    subgraph "External Systems"
        Server[Robot World Server<br/>TCP/IP endpoint]
        ConfigFiles[Configuration Files<br/>Robot makes<br/>Client settings]
        User[User<br/>Command input<br/>Visual feedback]
    end

    %% Component Relationships
    User --> CLI
    CLI --> CommandProcessor
    CommandProcessor --> ClientApp
    ClientApp --> RobotController
    ClientApp --> Display

    RobotController --> RobotModel
    RobotController --> WorldView
    RobotController --> NetworkClient

    NetworkClient --> MessageSerializer
    NetworkClient --> ConnectionManager
    ConnectionManager --> Server

    ConfigLoader --> ConfigFiles
    ConfigLoader --> RobotFactory
    RobotFactory --> RobotMake
    RobotFactory --> RobotModel

    Display --> WorldView
    Display --> RobotModel

    %% Data Flow
    MessageSerializer -.->|"JSON messages"| Server
    Server -.->|"JSON responses"| MessageSerializer
    WorldView -.->|"world state"| Display
    RobotModel -.->|"robot status"| Display
