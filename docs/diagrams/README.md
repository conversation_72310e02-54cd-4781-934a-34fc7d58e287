# Robot World - Design Diagrams

This directory contains the visual design diagrams for the Robot World project.

## Viewing the Diagrams

### Option 1: Direct SVG Files
- Open the `.svg` files directly in any web browser
- SVG files are scalable and maintain quality at any zoom level

### Option 2: Mermaid Live Editor
1. Go to [Mermaid Live Editor](https://mermaid.live/)
2. Copy the mermaid code from the `.mmd` files
3. <PERSON><PERSON> into the editor to view and export

### Option 3: GitLab Wiki
- Upload the SVG files to your GitLab project wiki
- Reference them in wiki pages using: `![Diagram Name](diagram-file.svg)`

### Option 4: VS Code with Mermaid Extension
- Install the "Mermaid Markdown Syntax Highlighting" extension
- View `.mmd` files with live preview

## Diagram Files

1. **class-diagram.svg** - Shows the most important classes and their relationships
2. **domain-diagram.svg** - Characterizes domain classes and their interactions  
3. **client-component-diagram.svg** - Client application architecture
4. **server-component-diagram.svg** - Server application architecture

## Source Files

The `.mmd` files contain the Mermaid source code for each diagram, which can be:
- Edited and updated as the design evolves
- Used to regenerate SVG files
- Shared with team members for collaborative editing
