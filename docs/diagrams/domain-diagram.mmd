graph TB
    subgraph "Game World Domain"
        World[World<br/>Grid-based battlefield<br/>Configurable size]
        Grid[Grid System<br/>Coordinate system<br/>Position tracking]
        Visibility[Visibility System<br/>Line of sight<br/>Range limitations]
    end

    subgraph "Robot Domain"
        Robot[Robot<br/>Player entity<br/>Named & typed]
        Movement[Movement System<br/>Forward/back/turn<br/>Obstacle avoidance]
        Orientation[Orientation<br/>N/S/E/W facing<br/>Direction tracking]
    end

    subgraph "Combat Domain"
        Weapon[Weapon System<br/>Shots & range<br/>Reload mechanics]
        Shield[Shield System<br/>Damage absorption<br/>Repair mechanics]
        Combat[Combat Resolution<br/>Hit detection<br/>Damage calculation]
    end

    subgraph "Obstacle Domain"
        Mountain[Mountain<br/>Blocks movement<br/>Blocks vision]
        Lake[Lake<br/>Blocks movement<br/>Allows vision]
        Pit[Bottomless Pit<br/>Destroys robots<br/>Allows vision]
    end

    subgraph "Configuration Domain"
        WorldConfig[World Configuration<br/>Size, visibility<br/>Timing parameters]
        RobotMakes[Robot Makes<br/>Different configurations<br/>Sniper, Tank, etc.]
    end

    subgraph "Communication Domain"
        Protocol[TCP/IP Protocol<br/>JSON messages<br/>Request-response]
        Commands[Command System<br/>Robot actions<br/>World queries]
    end

    %% Domain Relationships
    World --> Grid
    World --> Visibility
    World --> Mountain
    World --> Lake
    World --> Pit
    World --> Robot

    Robot --> Movement
    Robot --> Orientation
    Robot --> Weapon
    Robot --> Shield
    Robot --> Combat

    Movement --> Grid
    Movement --> Mountain
    Movement --> Lake
    Movement --> Pit

    Visibility --> Grid
    Visibility --> Mountain
    
    Combat --> Weapon
    Combat --> Shield
    Combat --> Robot

    WorldConfig --> World
    WorldConfig --> Visibility
    WorldConfig --> Shield
    WorldConfig --> Weapon

    RobotMakes --> Robot
    RobotMakes --> Weapon
    RobotMakes --> Shield

    Protocol --> Commands
    Commands --> Robot
    Commands --> World

    %% Key interactions
    Robot -.->|"looks around"| Visibility
    Robot -.->|"fires at"| Robot
    Robot -.->|"moves through"| Grid
    Weapon -.->|"limited by"| Visibility
    Shield -.->|"protects from"| Combat
