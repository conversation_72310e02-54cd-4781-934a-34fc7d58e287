<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robot World - Design Diagrams</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            background: white;
            margin: 30px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #007acc;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .description {
            color: #666;
            font-style: italic;
            margin-bottom: 20px;
        }
        .mermaid {
            text-align: center;
        }
        .export-buttons {
            text-align: center;
            margin: 10px 0;
        }
        .export-btn {
            background: #007acc;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
            text-decoration: none;
            display: inline-block;
        }
        .export-btn:hover {
            background: #005a9e;
        }
    </style>
</head>
<body>
    <h1>Robot World - Design Diagrams</h1>
    
    <div class="diagram-container">
        <h2>Class Diagram</h2>
        <div class="description">Shows the most important classes and their relationships in the Robot World system</div>
        <div class="export-buttons">
            <a href="class-diagram.mmd" class="export-btn" download>Download Mermaid Source</a>
        </div>
        <div class="mermaid" id="class-diagram">
classDiagram
    class World {
        -int width
        -int height
        -int visibilityRange
        -List~Robot~ robots
        -List~Obstacle~ obstacles
        -WorldConfiguration config
        +addRobot(Robot robot) boolean
        +removeRobot(String name) boolean
        +getRobots() List~Robot~
        +getObstacles() List~Obstacle~
        +isValidPosition(Position pos) boolean
        +isObstructed(Position pos) boolean
        +getRobotsInRange(Position pos, int range) List~Robot~
        +dump() String
    }

    class Robot {
        -String name
        -String make
        -Position position
        -Direction direction
        -Shield shield
        -Weapon weapon
        -RobotState state
        +move(int steps) boolean
        +turn(Direction dir) void
        +look() LookResult
        +fire() boolean
        +repair() void
        +reload() void
        +getState() RobotState
        +takeDamage() boolean
        +isAlive() boolean
    }

    class Position {
        -int x
        -int y
        +getX() int
        +getY() int
        +distanceTo(Position other) int
        +equals(Object obj) boolean
    }

    class Direction {
        &lt;&lt;enumeration&gt;&gt;
        NORTH
        SOUTH
        EAST
        WEST
        +turnLeft() Direction
        +turnRight() Direction
        +opposite() Direction
    }

    class Shield {
        -int currentStrength
        -int maxStrength
        -boolean repairing
        +takeDamage() boolean
        +repair(int repairTime) void
        +isDestroyed() boolean
        +getStrength() int
    }

    class Weapon {
        -int shots
        -int maxShots
        -int range
        -boolean reloading
        +fire() boolean
        +reload(int reloadTime) void
        +canFire() boolean
        +getShots() int
    }

    class Obstacle {
        &lt;&lt;abstract&gt;&gt;
        -Position topLeft
        -Position bottomRight
        +contains(Position pos) boolean
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class Mountain {
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class Lake {
        +blocksMovement() boolean
        +blocksVision() boolean
    }

    class BottomlessPit {
        +blocksMovement() boolean
        +blocksVision() boolean
        +destroysRobot() boolean
    }

    class WorldConfiguration {
        -int worldWidth
        -int worldHeight
        -int visibilityRange
        -int shieldRepairTime
        -int weaponReloadTime
        -int maxShieldStrength
        +loadFromFile(String filename) WorldConfiguration
    }

    class RobotState {
        -Position position
        -Direction direction
        -int shieldStrength
        -int shots
        -String status
    }

    class LookResult {
        -List~Obstacle~ obstacles
        -List~Robot~ robots
        -List~String~ edges
    }

    %% Relationships
    World "1" --&gt; "*" Robot : contains
    World "1" --&gt; "*" Obstacle : contains
    World "1" --&gt; "1" WorldConfiguration : uses
    Robot "1" --&gt; "1" Position : has
    Robot "1" --&gt; "1" Direction : faces
    Robot "1" --&gt; "1" Shield : has
    Robot "1" --&gt; "1" Weapon : has
    Robot "1" --&gt; "1" RobotState : provides
    Obstacle &lt;|-- Mountain : extends
    Obstacle &lt;|-- Lake : extends
    Obstacle &lt;|-- BottomlessPit : extends
    Obstacle "1" --&gt; "2" Position : defined by
    Robot --&gt; LookResult : creates
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#007acc',
                primaryTextColor: '#333',
                primaryBorderColor: '#007acc',
                lineColor: '#666'
            }
        });
    </script>
</body>
</html>
